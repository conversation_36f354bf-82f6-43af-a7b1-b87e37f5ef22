'use client';

import { useEffect, useState } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import Link from 'next/link';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { <PERSON><PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Skeleton } from '@/components/ui/skeleton';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';
import { Breadcrumb, BreadcrumbItem, BreadcrumbLink, BreadcrumbList, BreadcrumbPage, BreadcrumbSeparator } from '@/components/ui/breadcrumb';
import { Input } from '@/components/ui/input';
import { BulkNudgeModal } from '@/components/msme/bulk-nudge-modal';
import { api } from '@/lib/api';
import { MSME } from '@/types';
import { ArrowLeft, Building2, MapPin, Calendar, TrendingUp, TrendingDown, Minus, AlertTriangle, Search, Filter, BarChart3, MessageSquare, Users } from 'lucide-react';

export function PortfolioList() {
  const [msmes, setMsmes] = useState<MSME[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [showBulkNudgeModal, setShowBulkNudgeModal] = useState(false);
  const router = useRouter();
  const searchParams = useSearchParams();
  const filterRisk = searchParams.get('filter');

  useEffect(() => {
    async function fetchPortfolio() {
      try {
        setLoading(true);
        const data = await api.getPortfolio();
        setMsmes(data);
        setError(null);
      } catch (err) {
        setError(err instanceof Error ? err.message : 'Failed to fetch portfolio');
      } finally {
        setLoading(false);
      }
    }

    fetchPortfolio();
  }, []);

  const getRiskBadgeVariant = (risk: string) => {
    switch (risk) {
      case 'green': return 'default';
      case 'yellow': return 'secondary';
      case 'red': return 'destructive';
      default: return 'outline';
    }
  };

  const getRiskLabel = (risk: string) => {
    switch (risk) {
      case 'green': return 'Low';
      case 'yellow': return 'Medium';
      case 'red': return 'High';
      default: return 'Unknown';
    }
  };

  const getTrendIcon = (trend?: string) => {
    switch (trend) {
      case 'improving': return <TrendingUp className="h-4 w-4 text-green-500" />;
      case 'declining': return <TrendingDown className="h-4 w-4 text-red-500" />;
      case 'stable': return <Minus className="h-4 w-4 text-gray-500" />;
      default: return <Minus className="h-4 w-4 text-gray-500" />;
    }
  };

  const handleBulkNudgesSent = (count: number) => {
    // Show success message or refresh data if needed
    console.log(`Successfully sent ${count} nudges`);
    setShowBulkNudgeModal(false);
  };

  const filteredMsmes = msmes.filter(msme => {
    const matchesSearch = searchTerm === '' ||
      msme.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      msme.location.toLowerCase().includes(searchTerm.toLowerCase()) ||
      msme.business_type.toLowerCase().includes(searchTerm.toLowerCase()) ||
      msme.tags.some(tag => tag.toLowerCase().includes(searchTerm.toLowerCase()));

    const matchesRisk = !filterRisk || msme.risk_band === filterRisk;

    return matchesSearch && matchesRisk;
  });

  if (loading) {
    return (
      <div className="p-6 space-y-6">
        {/* Breadcrumb Skeleton */}
        <Skeleton className="h-4 w-32" />

        {/* Header Skeleton */}
        <div className="space-y-2">
          <Skeleton className="h-8 w-64" />
          <Skeleton className="h-4 w-48" />
        </div>

        {/* Search and Filter Skeleton */}
        <div className="flex gap-4">
          <Skeleton className="h-10 flex-1 max-w-sm" />
          <Skeleton className="h-10 w-24" />
        </div>

        {/* Table Skeleton */}
        <Card className="border-0 shadow-lg">
          <CardHeader>
            <Skeleton className="h-6 w-48" />
            <Skeleton className="h-4 w-64" />
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {[...Array(8)].map((_, i) => (
                <div key={i} className="flex items-center space-x-4 p-4 border rounded-lg">
                  <Skeleton className="h-12 w-12 rounded-lg" />
                  <div className="space-y-2 flex-1">
                    <Skeleton className="h-4 w-48" />
                    <Skeleton className="h-3 w-32" />
                  </div>
                  <Skeleton className="h-6 w-16" />
                  <Skeleton className="h-6 w-20" />
                  <Skeleton className="h-6 w-12" />
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  if (error) {
    return (
      <div className="container mx-auto p-6">
        <Card className="border-destructive">
          <CardHeader>
            <CardTitle className="text-destructive">Error Loading Portfolio</CardTitle>
            <CardDescription>{error}</CardDescription>
          </CardHeader>
          <CardContent>
            <Button onClick={() => window.location.reload()}>Retry</Button>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <TooltipProvider>
      <div className="p-6 space-y-6">
        {/* Breadcrumb */}
        <Breadcrumb>
          <BreadcrumbList>
            <BreadcrumbItem>
              <BreadcrumbLink href="/">Analytics</BreadcrumbLink>
            </BreadcrumbItem>
            <BreadcrumbSeparator />
            <BreadcrumbItem>
              <BreadcrumbPage className="text-foreground font-medium">
                Portfolio
                {filterRisk && ` - ${getRiskLabel(filterRisk)} Risk`}
              </BreadcrumbPage>
            </BreadcrumbItem>
          </BreadcrumbList>
        </Breadcrumb>

        {/* Header */}
        <div className="flex flex-col gap-4 md:flex-row md:items-center md:justify-between">
          <div className="space-y-2">
            <h1 className="text-4xl font-bold tracking-tight bg-gradient-to-r from-foreground to-foreground/70 bg-clip-text">
              MSME Portfolio
              {filterRisk && (
                <span className="ml-2 text-2xl font-normal text-muted-foreground">
                  {getRiskLabel(filterRisk)} Risk
                </span>
              )}
            </h1>
            <div className="flex items-center gap-4 text-muted-foreground">
              <span className="flex items-center gap-1">
                <Building2 className="h-4 w-4" />
                {filteredMsmes.length} of {msmes.length} MSMEs
              </span>
              {filterRisk && (
                <Button
                  variant="link"
                  className="p-0 h-auto text-primary hover:text-primary/80"
                  onClick={() => router.push('/portfolio')}
                >
                  Clear filter
                </Button>
              )}
            </div>
          </div>

          <div className="flex gap-3">
            <Link href="/">
              <Button variant="outline" className="shadow-sm">
                <ArrowLeft className="mr-2 h-4 w-4" />
                Back to Dashboard
              </Button>
            </Link>
          </div>
        </div>

        {/* Search and Filters */}
        <div className="flex flex-col gap-4 md:flex-row md:items-center md:justify-between">
          <div className="relative flex-1 max-w-sm">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
            <Input
              placeholder="Search MSMEs by name, location, or tags..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-10 shadow-sm"
            />
          </div>

          <div className="flex gap-2">
            <Tooltip>
              <TooltipTrigger asChild>
                <Button
                  variant={filterRisk === 'red' ? 'default' : 'outline'}
                  size="sm"
                  onClick={() => router.push('/portfolio?filter=red')}
                  className="shadow-sm"
                >
                  <AlertTriangle className="mr-2 h-4 w-4" />
                  High Risk ({msmes.filter(m => m.risk_band === 'red').length})
                </Button>
              </TooltipTrigger>
              <TooltipContent>
                <p>Show only high-risk MSMEs</p>
              </TooltipContent>
            </Tooltip>

            <Tooltip>
              <TooltipTrigger asChild>
                <Button
                  variant={filterRisk === 'yellow' ? 'default' : 'outline'}
                  size="sm"
                  onClick={() => router.push('/portfolio?filter=yellow')}
                  className="shadow-sm"
                >
                  <Filter className="mr-2 h-4 w-4" />
                  Medium Risk ({msmes.filter(m => m.risk_band === 'yellow').length})
                </Button>
              </TooltipTrigger>
              <TooltipContent>
                <p>Show only medium-risk MSMEs</p>
              </TooltipContent>
            </Tooltip>

            <Tooltip>
              <TooltipTrigger asChild>
                <Button
                  variant="default"
                  size="sm"
                  onClick={() => setShowBulkNudgeModal(true)}
                  className="shadow-sm bg-primary hover:bg-primary/90"
                  disabled={msmes.length === 0}
                >
                  <MessageSquare className="mr-2 h-4 w-4" />
                  Bulk Nudge
                </Button>
              </TooltipTrigger>
              <TooltipContent>
                <p>Send nudges to multiple MSMEs</p>
              </TooltipContent>
            </Tooltip>
          </div>
        </div>

        {/* Portfolio Table */}
        <Card className="border-0 shadow-lg">
          <CardHeader className="pb-4">
            <CardTitle className="flex items-center gap-2">
              <div className="p-2 bg-primary/10 rounded-lg">
                <Building2 className="h-5 w-5 text-primary" />
              </div>
              Portfolio Overview
              <Badge variant="secondary" className="ml-auto">
                {filteredMsmes.length} MSMEs
              </Badge>
            </CardTitle>
            <CardDescription>
              Click on any MSME row to view detailed information and score breakdown
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="rounded-lg border overflow-hidden">
              <Table>
                <TableHeader>
                  <TableRow className="bg-muted/50">
                    <TableHead className="font-semibold">Business</TableHead>
                    <TableHead className="font-semibold">Type</TableHead>
                    <TableHead className="font-semibold">Location</TableHead>
                    <TableHead className="font-semibold text-center">Score</TableHead>
                    <TableHead className="font-semibold text-center">Risk</TableHead>
                    <TableHead className="font-semibold text-center">Trend</TableHead>
                    <TableHead className="font-semibold text-center">Signals</TableHead>
                    <TableHead className="font-semibold">Last Activity</TableHead>
                    <TableHead className="font-semibold text-center">Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {filteredMsmes.map((msme) => (
                    <TableRow
                      key={msme.msme_id}
                      className="group hover:bg-muted/30 transition-colors duration-200 border-b"
                    >
                      <TableCell className="font-medium">
                        <div className="flex items-center gap-3">
                          <div className="flex-shrink-0">
                            <div className="w-10 h-10 rounded-lg bg-gradient-to-br from-primary/20 to-primary/10 flex items-center justify-center">
                              <Building2 className="h-5 w-5 text-primary" />
                            </div>
                          </div>
                          <div className="flex flex-col min-w-0">
                            <span className="font-semibold text-foreground truncate">{msme.name}</span>
                            <div className="flex gap-1 mt-1 flex-wrap">
                              {msme.tags.slice(0, 2).map((tag) => (
                                <Badge key={tag} variant="outline" className="text-xs px-1.5 py-0.5">
                                  {tag}
                                </Badge>
                              ))}
                              {msme.tags.length > 2 && (
                                <Badge variant="outline" className="text-xs px-1.5 py-0.5">
                                  +{msme.tags.length - 2}
                                </Badge>
                              )}
                            </div>
                          </div>
                        </div>
                      </TableCell>
                      <TableCell>
                        <Badge variant="secondary" className="capitalize font-medium">
                          {msme.business_type}
                        </Badge>
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center gap-2">
                          <MapPin className="h-4 w-4 text-muted-foreground flex-shrink-0" />
                          <span className="text-sm text-muted-foreground truncate">{msme.location}</span>
                        </div>
                      </TableCell>
                      <TableCell className="text-center">
                        <div className="flex flex-col items-center">
                          <span className="text-2xl font-bold font-mono text-foreground">
                            {msme.current_score}
                          </span>
                          <div className="w-12 h-1 rounded-full bg-muted mt-1">
                            <div
                              className={`h-full rounded-full transition-all duration-300 ${
                                msme.risk_band === 'green' ? 'bg-green-500' :
                                msme.risk_band === 'yellow' ? 'bg-yellow-500' : 'bg-red-500'
                              }`}
                              style={{ width: `${(msme.current_score / 1000) * 100}%` }}
                            />
                          </div>
                        </div>
                      </TableCell>
                      <TableCell className="text-center">
                        <Badge
                          variant={getRiskBadgeVariant(msme.risk_band)}
                          className="font-medium"
                        >
                          {getRiskLabel(msme.risk_band)}
                        </Badge>
                      </TableCell>
                      <TableCell className="text-center">
                        <div className="flex items-center justify-center gap-2">
                          {getTrendIcon(msme.score_trend)}
                          <span className="text-sm capitalize font-medium">
                            {msme.score_trend || 'stable'}
                          </span>
                        </div>
                      </TableCell>
                      <TableCell className="text-center">
                        <div className="flex items-center justify-center gap-2">
                          <div className="flex items-center gap-1">
                            <BarChart3 className="h-4 w-4 text-muted-foreground" />
                            <span className="font-medium">{msme.signals_count}</span>
                          </div>
                          {msme.recent_nudges > 0 && (
                            <Tooltip>
                              <TooltipTrigger asChild>
                                <Badge variant="destructive" className="text-xs px-1.5 py-0.5">
                                  <AlertTriangle className="h-3 w-3 mr-1" />
                                  {msme.recent_nudges}
                                </Badge>
                              </TooltipTrigger>
                              <TooltipContent>
                                <p>{msme.recent_nudges} recent alerts</p>
                              </TooltipContent>
                            </Tooltip>
                          )}
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center gap-2 text-sm text-muted-foreground">
                          <Calendar className="h-4 w-4" />
                          <span>{new Date(msme.last_signal_date).toLocaleDateString()}</span>
                        </div>
                      </TableCell>
                      <TableCell className="text-center">
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => router.push(`/msme/${msme.msme_id}`)}
                          className="opacity-0 group-hover:opacity-100 transition-opacity duration-200"
                        >
                          View MSME
                        </Button>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </div>

            {filteredMsmes.length === 0 && (
              <div className="text-center py-12">
                <div className="flex flex-col items-center gap-4">
                  <div className="w-16 h-16 rounded-full bg-muted flex items-center justify-center">
                    <Building2 className="h-8 w-8 text-muted-foreground" />
                  </div>
                  <div className="space-y-2">
                    <h3 className="text-lg font-semibold">No MSMEs found</h3>
                    <p className="text-muted-foreground">
                      {searchTerm ? (
                        <>No MSMEs match your search criteria "{searchTerm}"</>
                      ) : filterRisk ? (
                        <>No MSMEs found with {getRiskLabel(filterRisk).toLowerCase()} risk level</>
                      ) : (
                        <>Your portfolio is empty. Add some MSMEs to get started.</>
                      )}
                    </p>
                  </div>
                  {(searchTerm || filterRisk) && (
                    <Button
                      variant="outline"
                      onClick={() => {
                        setSearchTerm('');
                        router.push('/portfolio');
                      }}
                    >
                      Clear filters
                    </Button>
                  )}
                </div>
              </div>
            )}
          </CardContent>
        </Card>

        {/* Bulk Nudge Modal */}
        <BulkNudgeModal
          isOpen={showBulkNudgeModal}
          onClose={() => setShowBulkNudgeModal(false)}
          onNudgesSent={handleBulkNudgesSent}
        />
      </div>
    </TooltipProvider>
  );
}
