from fastapi import FastAP<PERSON>
from fastapi.middleware.cors import CORSMiddleware
from dotenv import load_dotenv
import os

# Load environment variables
load_dotenv()

# Initialize demo data
try:
    from init_demo_data import init_demo_data
    init_demo_data()
except Exception as e:
    print(f"Warning: Could not initialize demo data: {e}")

# Initialize FastAPI app
app = FastAPI(
    title="Credit Chakra API",
    description="MSME Credit Scoring and Monitoring Platform",
    version="1.0.0"
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # Configure this properly for production
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Import routers
from routes import msme, signals, nudges, dashboard, data_refresh

# Include routers
app.include_router(msme.router, prefix="/msme", tags=["MSME"])
app.include_router(signals.router, prefix="/api/signals", tags=["Signals"])
app.include_router(nudges.router, prefix="", tags=["Nudges"])  # No prefix for nudges to allow /msme/{id}/nudges
app.include_router(dashboard.router, prefix="/dashboard", tags=["Dashboard"])
app.include_router(data_refresh.router, prefix="/api", tags=["Data Refresh"])

@app.get("/")
async def root():
    return {"message": "Credit Chakra API is running"}

@app.get("/health")
async def health_check():
    return {"status": "healthy", "service": "credit-chakra-backend"}

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8000)
